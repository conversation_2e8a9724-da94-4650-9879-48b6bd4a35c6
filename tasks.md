| ID | Task | Status | Feature | Description |
|----|------|--------|---------|-------------|
| T1 | Add 'Date Booked' to Contracts | Doing | Contracts | Infinity wants to display when an event is "Booked". The booked status is a workflow 'state' on the project/event. we do not have a way to track individual states so we will need to perform several tasks in order to setup this feature. 1) Add 'date_booked' to companies blueprint 2) Find inserstion point of workflow system change and add a clause to identify the workflow state and update the 'date_booked' field. 3) verify that new 'date_booked' field is populating correctly. 4) Register the 'date_booked' Merge Tag to allow use within the Document/Contract system. 5) Add the 'date_booked' merge tag to the standard contract template. 6) Test new contract to validate 'date_booked' is populating correctly. |
