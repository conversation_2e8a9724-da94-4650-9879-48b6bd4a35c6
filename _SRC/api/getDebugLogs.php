<?php
// Debug log viewer for production troubleshooting
// Returns recent error log entries related to tax calculations

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Get the error log file path
$errorLogPath = ini_get('error_log');
if (!$errorLogPath || !file_exists($errorLogPath)) {
    // Try common locations
    $possiblePaths = [
        '/var/log/apache2/error.log',
        '/var/log/httpd/error_log',
        '/var/log/php_errors.log',
        '../logs/error.log',
        'error.log'
    ];
    
    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            $errorLogPath = $path;
            break;
        }
    }
}

if (!$errorLogPath || !file_exists($errorLogPath)) {
    echo json_encode([
        'error' => 'Error log file not found',
        'searched_paths' => $possiblePaths ?? [],
        'ini_error_log' => ini_get('error_log')
    ]);
    exit;
}

try {
    // Read the last 1000 lines of the error log
    $lines = [];
    $handle = fopen($errorLogPath, 'r');
    
    if ($handle) {
        // Get file size and start from near the end
        fseek($handle, -50000, SEEK_END); // Start 50KB from end
        
        while (($line = fgets($handle)) !== false) {
            $lines[] = $line;
        }
        fclose($handle);
        
        // Keep only the last 1000 lines
        $lines = array_slice($lines, -1000);
        
        // Filter for tax-related debug entries
        $taxDebugLines = [];
        $currentEntry = '';
        
        foreach ($lines as $line) {
            if (strpos($line, 'TAX') !== false || 
                strpos($line, 'PRICING') !== false ||
                strpos($line, 'Processing tax') !== false ||
                strpos($line, 'Processing item') !== false) {
                
                $taxDebugLines[] = trim($line);
            }
        }
        
        echo json_encode([
            'success' => true,
            'log_file' => $errorLogPath,
            'total_lines' => count($lines),
            'tax_debug_lines' => $taxDebugLines,
            'last_50_lines' => array_slice($lines, -50), // Also include last 50 lines for context
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } else {
        echo json_encode([
            'error' => 'Could not open error log file',
            'log_file' => $errorLogPath
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'Exception reading log file: ' . $e->getMessage(),
        'log_file' => $errorLogPath
    ]);
}
?>
