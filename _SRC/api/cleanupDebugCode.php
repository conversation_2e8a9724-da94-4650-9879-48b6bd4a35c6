<?php
// Cleanup script to remove debug code after issue is resolved
// This script will remove the debug logging from the pricing calculation

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
if (!$input || !isset($input['confirm']) || $input['confirm'] !== 'yes') {
    echo json_encode(['error' => 'Confirmation required. Send {"confirm": "yes"}']);
    exit;
}

try {
    $changes = [];
    
    // Clean up PHP file
    $phpFile = '../pagoda/rules/actions/setMenuPricingRecord.php';
    if (file_exists($phpFile)) {
        $content = file_get_contents($phpFile);
        
        // Remove debug logging blocks
        $patterns = [
            '/\s*\/\/ DEBUG:.*?\n/',
            '/\s*error_log\("===.*?\n/',
            '/\s*error_log\("Processing.*?\n/',
            '/\s*error_log\("Found.*?\n/',
            '/\s*error_log\("Using.*?\n/',
            '/\s*error_log\("Final.*?\n/',
            '/\s*error_log\("Added.*?\n/',
            '/\s*error_log\("Set.*?\n/',
            '/\s*error_log\("Applied.*?\n/',
            '/\s*error_log\("Looking.*?\n/',
            '/\s*error_log\("Category.*?\n/',
            '/\s*error_log\("Skipped.*?\n/',
            '/\s*error_log\("Current.*?\n/',
            '/\s*error_log\("Number.*?\n/',
            '/\s*error_log\("Tax.*?\n/',
            '/\s*error_log\("Client.*?\n/',
            '/\s*error_log\("Initial.*?\n/',
            '/\s*error_log\("Menu.*?\n/'
        ];
        
        $originalLength = strlen($content);
        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }
        
        if (strlen($content) !== $originalLength) {
            file_put_contents($phpFile, $content);
            $changes[] = 'Cleaned up PHP debug logging in setMenuPricingRecord.php';
        }
    }
    
    // Clean up JavaScript file
    $jsFile = '../notify/_components/_bizdev/inventory.js';
    if (file_exists($jsFile)) {
        $content = file_get_contents($jsFile);
        
        // Remove debug button creation and logging
        $patterns = [
            '/\s*\/\/ DEBUG:.*?\n/',
            '/\s*console\.log\(\'===.*?\n/',
            '/\s*console\.log\(\'pricingBreakdown.*?\n/',
            '/\s*console\.log\(\'tax_rates.*?\n/',
            '/\s*console\.log\(\'=== PROJECT STATUS.*?\n/',
            '/\s*\/\/ Add button to fetch PHP debug logs.*?document\.body\.appendChild\(debugBtn\);\s*}/s',
            '/\s*\/\/ Add button to test tax fallback fix.*?document\.body\.appendChild\(testBtn\);\s*}/s',
            '/\s*\/\/ Email debug function.*?}\s*}/s'
        ];
        
        $originalLength = strlen($content);
        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }
        
        if (strlen($content) !== $originalLength) {
            file_put_contents($jsFile, $content);
            $changes[] = 'Cleaned up JavaScript debug code in inventory.js';
        }
    }
    
    echo json_encode([
        'success' => true,
        'changes' => $changes,
        'message' => 'Debug code cleanup completed',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Cleanup failed: ' . $e->getMessage()
    ]);
}
?>
