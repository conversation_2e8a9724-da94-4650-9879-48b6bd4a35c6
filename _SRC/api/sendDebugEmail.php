<?php
// Debug email sender for production troubleshooting
// Uses mailspons SMTP for reliable delivery

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['to']) || !isset($input['subject']) || !isset($input['body'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required fields: to, subject, body']);
    exit;
}

// Use PHPMailer for SMTP
require_once '../vendor/autoload.php';
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

try {
    $mail = new PHPMailer(true);
    
    // SMTP configuration for mailspons
    $mail->isSMTP();
    $mail->Host = $input['smtp']['host'] ?? 'smtp.mailspons.com';
    $mail->SMTPAuth = true;
    $mail->Username = $input['smtp']['username'] ?? 'e3d1576329f340ebaca8';
    $mail->Password = $input['smtp']['password'] ?? '1eb86be350664daa913f1bd702cb8384';
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = $input['smtp']['port'] ?? 587;
    
    // Email content
    $mail->setFrom($input['smtp']['username'] . '@mailspons.com', 'Bento Debug System');
    $mail->addAddress($input['to']);
    $mail->Subject = $input['subject'];
    $mail->Body = $input['body'];
    $mail->isHTML(false);
    
    $mail->send();
    
    echo json_encode([
        'success' => true,
        'message' => 'Debug email sent successfully',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log('Debug email failed: ' . $e->getMessage());
    
    // Fallback: write to log file if email fails
    $logFile = '../logs/debug_' . date('Y-m-d') . '.log';
    $logEntry = date('Y-m-d H:i:s') . " - " . $input['subject'] . "\n" . $input['body'] . "\n\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    echo json_encode([
        'success' => false,
        'error' => 'Email failed, logged to file: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
