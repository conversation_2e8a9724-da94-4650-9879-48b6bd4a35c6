/**
 * Export product categories and groups to Excel or ZIP of CSVs
 * This script handles large datasets by processing in batches
 */

// ZIP approach with multiple CSVs
function exportProductsToZip() {
  // First, check if JSZip is available
  if (typeof JSZip === 'undefined') {
    console.log('Loading JSZip library...');
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
    script.onload = function() {
      console.log('JSZip loaded, starting export...');
      startExport();
    };
    document.head.appendChild(script);
  } else {
    startExport();
  }

  function startExport() {
    // Create a new ZIP file
    const zip = new JSZip();
    let categoriesProcessed = 0;
    let totalCategories = 0;

    // Get all regular categories
    databaseConnection.obj.getAll('inventory_billable_categories', function(regularCategories) {
      console.log(`Found ${regularCategories.length} regular product categories`);

      // Get all combination categories
      databaseConnection.obj.getAll('inventory_billable_combination_categories', function(combinationCategories) {
        console.log(`Found ${combinationCategories.length} combination product categories`);

        // Prepare all categories with type information
        const allCategories = [
          ...regularCategories.map(cat => ({...cat, type: 'regular'})),
          ...combinationCategories.map(cat => ({...cat, type: 'combination'}))
        ];

        totalCategories = allCategories.length;
        console.log(`Processing ${totalCategories} total product categories...`);

        // Process each category
        processNextCategory(0, allCategories);
      });
    });

    function processNextCategory(index, allCategories) {
      if (index >= allCategories.length) {
        // All categories processed, generate and download ZIP
        console.log('All categories processed, generating ZIP file...');
        zip.generateAsync({type: 'blob'})
          .then(function(content) {
            // Create download link
            const link = document.createElement('a');
            link.href = URL.createObjectURL(content);
            link.download = 'product_categories.zip';
            document.body.appendChild(link);

            // Trigger download
            link.click();

            // Clean up
            document.body.removeChild(link);
            console.log('Export complete!');
          });
        return;
      }

      const category = allCategories[index];
      console.log(`Processing category: ${category.name} (${index + 1}/${allCategories.length})`);

      // Determine which type of items to fetch based on category type
      const itemType = category.type === 'regular' ? 'inventory_billable_groups' : 'inventory_billable_combinations';

      // Get all items for this category
      databaseConnection.obj.getWhere(itemType, {category: category.id}, function(items) {
        console.log(`Found ${items.length} items for category ${category.name}`);

        // Create CSV content for this category
        let csvContent = "ID,Object UID,Name,Price,Updated Price\n";

        // Process items in batches
        const batchSize = 100;
        let currentIndex = 0;

        function processItemBatch() {
          const endIndex = Math.min(currentIndex + batchSize, items.length);

          // Process current batch
          for (let i = currentIndex; i < endIndex; i++) {
            const item = items[i];
            // Escape quotes in names
            const escapedName = item.name ? item.name.replace(/"/g, '""') : '';
            // Format price as currency
            const price = item.price ? `$${(item.price/100).toFixed(2)}` : '$0.00';

            csvContent += `${item.id},"${item.object_uid || ''}","${escapedName}","${price}",\n`;
          }

          currentIndex = endIndex;

          // Continue with next batch or finish this category
          if (currentIndex < items.length) {
            setTimeout(processItemBatch, 0);
          } else {
            // All items for this category processed
            // Add CSV to ZIP file - sanitize filename and add type prefix
            const prefix = category.type === 'regular' ? 'reg_' : 'comb_';
            const safeFileName = prefix + category.name.replace(/[^a-z0-9]/gi, '_').toLowerCase();
            zip.file(`${safeFileName}.csv`, csvContent);

            categoriesProcessed++;
            console.log(`Completed category ${categoriesProcessed}/${totalCategories}`);

            // Process next category
            setTimeout(function() {
              processNextCategory(index + 1, allCategories);
            }, 0);
          }
        }

        // Start processing items
        processItemBatch();
      });
    }
  }
}

// Excel approach with multiple sheets
function exportProductsToExcel() {
  // First, check if SheetJS is available
  if (typeof XLSX === 'undefined') {
    console.log('Loading SheetJS library...');
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
    script.onload = function() {
      console.log('SheetJS loaded, starting export...');
      startExport();
    };
    document.head.appendChild(script);
  } else {
    startExport();
  }

  function startExport() {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();
    let categoriesProcessed = 0;
    let totalCategories = 0;

    // Get all regular categories
    databaseConnection.obj.getAll('inventory_billable_categories', function(regularCategories) {
      console.log(`Found ${regularCategories.length} regular product categories`);

      // Get all combination categories
      databaseConnection.obj.getAll('inventory_billable_combination_categories', function(combinationCategories) {
        console.log(`Found ${combinationCategories.length} combination product categories`);

        // Prepare all categories with type information
        const allCategories = [
          ...regularCategories.map(cat => ({...cat, type: 'regular'})),
          ...combinationCategories.map(cat => ({...cat, type: 'combination'}))
        ];

        totalCategories = allCategories.length;
        console.log(`Processing ${totalCategories} total product categories...`);

        // Process each category
        processNextCategory(0, allCategories);
      });
    });

    function processNextCategory(index, allCategories) {
      if (index >= allCategories.length) {
        // All categories processed, generate and download Excel file
        console.log('All categories processed, generating Excel file...');

        // Generate Excel file
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        // Create download link
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'product_categories.xlsx';
        document.body.appendChild(link);

        // Trigger download
        link.click();

        // Clean up
        document.body.removeChild(link);
        console.log('Export complete!');
        return;
      }

      const category = allCategories[index];
      console.log(`Processing category: ${category.name} (${index + 1}/${allCategories.length})`);

      // Determine which type of items to fetch based on category type
      const itemType = category.type === 'regular' ? 'inventory_billable_groups' : 'inventory_billable_combinations';

      // Get all items for this category
      databaseConnection.obj.getWhere(itemType, {category: category.id}, function(items) {
        console.log(`Found ${items.length} items for category ${category.name}`);

        // Process items in batches
        const batchSize = 100;
        let currentIndex = 0;
        let sheetData = [["ID", "Object UID", "Name", "Price", "Updated Price"]]; // Header row with new column

        function processItemBatch() {
          const endIndex = Math.min(currentIndex + batchSize, items.length);

          // Process current batch
          for (let i = currentIndex; i < endIndex; i++) {
            const item = items[i];
            // Format price as currency
            const price = item.price ? `$${(item.price/100).toFixed(2)}` : '$0.00';

            sheetData.push([
              item.id,
              item.object_uid || '',
              item.name || '',
              price,
              '' // Empty "Updated Price" column
            ]);
          }

          currentIndex = endIndex;

          // Continue with next batch or finish this category
          if (currentIndex < items.length) {
            setTimeout(processItemBatch, 0);
          } else {
            // All items for this category processed
            // Create worksheet and add to workbook
            const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

            // Sanitize sheet name (Excel has 31 char limit and restrictions on special chars)
            // Add prefix to distinguish between regular and combination categories
            const prefix = category.type === 'regular' ? 'R_' : 'C_';
            let sheetName = prefix + category.name.substring(0, 29) // Leave room for prefix
              .replace(/[\[\]\*\/\\\?:]/g, ''); // Remove invalid chars

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

            categoriesProcessed++;
            console.log(`Completed category ${categoriesProcessed}/${totalCategories}`);

            // Process next category
            setTimeout(function() {
              processNextCategory(index + 1, allCategories);
            }, 0);
          }
        }

        // Start processing items
        processItemBatch();
      });
    }
  }
}

// Run the function of your choice
exportProductsToZip();
// exportProductsToExcel();

console.log("Product export functions loaded. Run either:");
console.log("- exportProductsToZip() for a ZIP file with multiple CSVs");
console.log("- exportProductsToExcel() for a single Excel file with multiple sheets");
